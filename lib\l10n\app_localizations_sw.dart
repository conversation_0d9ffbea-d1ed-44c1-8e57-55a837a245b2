// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Swahili (`sw`).
class AppLocalizationsSw extends AppLocalizations {
  AppLocalizationsSw([String locale = 'sw']) : super(locale);

  @override
  String get appName => 'Sepesha';

  @override
  String get deleteConversation => 'Futa Mazungumzo';

  @override
  String get areYouSureDeleteConversation => 'Una uhakika unataka kufuta mazungumzo haya na';

  @override
  String get home => 'Nyumbani';

  @override
  String helloWelcomeBack(Object userType) {
    return 'Hujambo! Karibu tena';
  }

  @override
  String get enterPhoneVerification => 'Ingiza nambari ya simu yako tutakutumia OTP code ya uhakiki';

  @override
  String get phoneNumber => 'Nambari ya simu';

  @override
  String get searchCountry => 'Tafuta nchi...';

  @override
  String get driver => 'Dereva';

  @override
  String get driverDescription => 'Ingia kama dereva kwa ajili ya kupata ombi la haraka kutoka kwa wateja na wachuuzi';

  @override
  String get vendorBusiness => 'Mfanyabiashara';

  @override
  String get vendorDescription => 'Ingia kama mchuuzi kudhibiti bidhaa zako na kupokea maagizo';

  @override
  String get customer => 'Mteja';

  @override
  String get customerDescription => 'Ingia kama mteja kuomba utoaji na kuagiza bidhaa';

  @override
  String get loginAsDriverOrVendor => 'Ingia kama dereva au mfanyabiashara';

  @override
  String get loginAsCustomerOrVendor => 'Ingia kama mteja au mfanyabiashara';

  @override
  String get loginAsCustomerOrDriver => 'Ingia kama mteja au dereva';

  @override
  String get login => 'Ingia';

  @override
  String get notRegistered => 'Hujasajiliwa? ';

  @override
  String get signUp => 'Jisajili';

  @override
  String get byContinuingYouAgree => 'Kwa kuendelea, unakubali ';

  @override
  String get termsConditions => 'Masharti';

  @override
  String get and => ' na ';

  @override
  String get privacyPolicy => 'Sera ya faragha';

  @override
  String get youreOver18 => '. Una umri wa miaka 18 au zaidi.';

  @override
  String get getStarted => 'ANZA';

  @override
  String get next => 'ENDELEA';

  @override
  String get trips => 'Safari';

  @override
  String get skip => 'Ruka';

  @override
  String get submitting => 'Inatuma...';

  @override
  String get submitRating => 'Tuma Kadiri';

  @override
  String get poor => 'Mbaya';

  @override
  String get fair => 'Wastani';

  @override
  String get good => 'Nzuri';

  @override
  String get tryAgain => 'Jaribu Tena';

  @override
  String get veryGood => 'Nzuri Sana';

  @override
  String get excellent => 'Bora Kabisa';

  @override
  String get rateYourExperience => 'Kadiri uzoefu wako';

  @override
  String get errorSigningOut => 'Imeshindikana kutoka';

  @override
  String get account => 'Akaunti';

  @override
  String get user => 'Mtumiaji';

  @override
  String get totalRides => 'Jumla ya Safari';

  @override
  String get rating => 'Kiwango';

  @override
  String get quickStats => 'Takwimu za Haraka';

  @override
  String get personalInformation => 'Maelezo ya Kibinafsi';

  @override
  String get manageProfileDetails => 'Dhibiti maelezo ya wasifu wako';

  @override
  String get paymentMethods => 'Njia za Malipo';

  @override
  String get managePaymentOptions => 'Dhibiti chaguo za malipo yako';

  @override
  String get messages => 'Ujumbe';

  @override
  String get viewConversations => 'Ona mazungumzo yako';

  @override
  String get settings => 'Mipangilio';

  @override
  String get appPreferencesSettings => 'Mapendeleo ya programu na mipangilio';

  @override
  String get helpSupport => 'Msaada na Uongozi';

  @override
  String get getHelpSupport => 'Pata msaada na uongozi';

  @override
  String get about => 'Kuhusu';

  @override
  String get appInformationVersion => 'Maelezo ya programu na toleo';

  @override
  String get logout => 'Toka';

  @override
  String get signOutAccount => 'Toka kwenye akaunti yako';

  @override
  String get logoutConfirmation => 'Je, una uhakika unataka kutoka kwenye akaunti yako?';

  @override
  String get cancel => 'Ghairi';

  @override
  String get searchConversations => 'Tafuta mazungumzo...';

  @override
  String get noResultsFound => 'Hakuna matokeo yaliyopatikana';

  @override
  String get trySearchingWithDifferentKeywords => 'Jaribu kutafuta kwa maneno tofauti';

  @override
  String get clearSearch => 'Futa utafutaji';

  @override
  String get noConversationsYet => 'Hakuna mazungumzo bado';

  @override
  String get startNewConversationToBeginMessaging => 'Anza mazungumzo mapya kuanza kutuma ujumbe';

  @override
  String get startNewChat => 'Anza mazungumzo mapya';

  @override
  String get somethingWentWrong => 'Kuna kitu kimekosea';

  @override
  String get markAllAsRead => 'Weka yote kama yamekwisha somwa';

  @override
  String get newConversation => 'Mazungumzo mapya';

  @override
  String get searchContacts => 'Tafuta anwani...';

  @override
  String get conversationDeleted => 'Mazungumzo yamefutwa';

  @override
  String get undo => 'Tendua';

  @override
  String get allConversationsMarkedAsRead => 'Mazungumzo yote yamewekwa kama yamekwisha somwa';

  @override
  String get messageSettingsComingSoon => 'Mipangilio ya ujumbe itakuja hivi karibuni';

  @override
  String get confirmLocation => 'Thibitisha Mahali';

  @override
  String get setPickupLocation => 'Weka mahali pa kukusanya mzigo';

  @override
  String get setDestination => 'Weka mahali pa kwenda mzigo';

  @override
  String get startTypingToSearch => 'Anza kuandika kutafuta maeneo';

  @override
  String get typeAtLeast2Characters => 'Andika angalau herufi 2';

  @override
  String get noLocationsFound => 'Hakuna maeneo yaliyopatikana';

  @override
  String get driverFound => 'Dereva Amepatikana';

  @override
  String get payment => 'Malipo';

  @override
  String get destination => 'Mahali pa Kwenda';

  @override
  String get isWaiting => 'anasubiri';

  @override
  String get language => 'Lugha';

  @override
  String get english => 'Kiingereza';

  @override
  String get swahili => 'Kiswahili';

  @override
  String get profile => 'Wasifu';

  @override
  String get edit => 'Hariri';

  @override
  String get save => 'Hifadhi';

  @override
  String get yes => 'Ndiyo';

  @override
  String get no => 'Hapana';

  @override
  String get languageSettings => 'Badili lugha';

  @override
  String get selectLanguage => 'Chagua Lugha';

  @override
  String get languageChanged => 'Lugha imebadilishwa kwa mafanikio';

  @override
  String get notifications => 'Arifa';

  @override
  String get notificationsComingSoon => 'Kipengele cha arifa kinakuja hivi karibuni!';

  @override
  String get filterTrips => 'Chuja safari!';

  @override
  String get vendor => 'MUUZAJI';

  @override
  String get main => 'Kuu';

  @override
  String get support => 'Msaada';

  @override
  String get appInformation => 'Taarifa kuhusu Sepesha';

  @override
  String get history => 'Historia';

  @override
  String get wallet => 'Mfukoni';

  @override
  String get manageWallet => 'Dhibiti mfuko wako';

  @override
  String get yourDriverHasArrived => 'Dereva wako amefika';

  @override
  String get contactUs => 'Wasiliana Nasi';

  @override
  String get whatsappChat => 'Chat kwa WhatsApp';

  @override
  String get quickResponseViaWhatsapp => 'Majibu haraka kwa WhatsApp';

  @override
  String get phoneCall => 'Simu';

  @override
  String get speakDirectlyWithOurTeam => 'Zungumza moja kwa moja na timu yetu';

  @override
  String get version => 'Toleo';

  @override
  String get keyFeatures => 'Vipengele Muhimu';

  @override
  String get contactInformation => 'Maelezo ya Mawasiliano';

  @override
  String get allRightsReserved => 'Haki zote zimehifadhiwa';

  @override
  String get saveTimeSaveMoneyAnd => 'Okoa muda, okolea pesa na';

  @override
  String get safeRide => 'Safari salama';

  @override
  String get useYourSmartphoneToOrder => 'Tumia simu yako ya mkononi kuagiza safari, kuchukuliwa na dereva aliyeko karibu, na kufurahia safari ya gharama ya chini hadi mahali unapokwenda.';

  @override
  String get getConnectedWith => 'Jiunge na';

  @override
  String get nearbyDrivers => 'dereva wa karibu';

  @override
  String get quicklyMatchWithReliable => 'Pata dereva wa kujiamini karibu nawe kwa haraka kwa kuchukua haraka zaidi na huduma bora zaidi.';

  @override
  String get enjoyARideWith => 'Furahia safari na';

  @override
  String get fullComfort => 'rahisi kabisa';

  @override
  String get relaxInWellMaintained => 'Pumzika katika magari yazuri yaliyohifadhiwa wakati dereva wako akiendesha barabarani.';

  @override
  String get ridesCompleted => 'Safari zilizofanikiwa';

  @override
  String get vehicleInfo => 'Taarifa za gari';

  @override
  String get vehicleType => 'Aina ya gari';

  @override
  String get vehicleNumber => 'Nambari ya gari';

  @override
  String get licenseNumber => 'Nambari ya leseni';

  @override
  String get locationAccessRequired => 'Ruhusa ya kupata mahali ulipo inahitajika';

  @override
  String get enableLocationPermissions => 'Gonga ili kuwezesha ruhusa za mahali ulipo ili uweze kuingia mtandaoni';

  @override
  String get youAreOnline => 'Uko mtandaoni';

  @override
  String get youAreOffline => 'Hauko mtandaoni, washa intaneti';

  @override
  String get waitingForRideRequests => 'Inasubiri ombi la safari...';

  @override
  String get pickup => 'Eneo la kukusanya mzigo';

  @override
  String get startRide => 'Anza safari';

  @override
  String get newRideRequest => 'Tafuta safari mpya';

  @override
  String get from => 'Kutoka:';

  @override
  String get to => 'Kwenda:';

  @override
  String get decline => 'Kataa';

  @override
  String get accept => 'Kubali';

  @override
  String get pickupLocation => 'Eneo la kukusanya mzigo';

  @override
  String get dropoffLocation => 'Eneo la kupeleka mzigo';

  @override
  String get refreshLocation => 'Sahihisha mahali ulipo';

  @override
  String get locationUpdated => 'Mahali ulipo pamebadilishwa:';

  @override
  String get couldNotGetLocation => 'Haikuwezekana kupata mahali ulipo kwaa sasa. Angalia ruhusa kwenye settings.';

  @override
  String get areYouSureLogout => 'Una uhakika unataka kutoka?';

  @override
  String get locationServicesDisabled => 'Huduma za mahali ulipo zimezimwa';

  @override
  String get enableLocationServices => 'Huduma za mahali ulipo zimezimwa. Tafadhali weka huduma za mahali ulipo katika settings za simu yako ili kuendelea.';

  @override
  String get openSettings => 'Fungua settings';

  @override
  String get locationPermissionRequired => 'Ruhusa ya mahali ulipo inahitajika';

  @override
  String get enableLocationPermission => 'Ruhusa ya mahali ulipo inahitajika ili uweze kuingia mtandaoni kama dereva. Tafadhali weka ruhusa ya mahali ulipo katika settings za simu.';

  @override
  String get locationPermissionDenied => 'Ruhusa ya mahali ulipo imekataliwa';

  @override
  String get grantLocationPermission => 'Ruhusa ya mahali ulipo inahitajika kutambua nafasi yako kama dereva. Tafadhali toa ruhusa ya mahali ulipo ili kuendelea.';

  @override
  String get locationPermissionsEnabled => 'Ruhusa za mahali ulipo zimewasha! Unaweza kuingia mtandaoni.';

  @override
  String get unableToGetLocationPermissions => 'Haikuwezekana kupata ruhusa za mahali ulipo. Tafadhali angalia settings za simu.';

  @override
  String get locationPermissionsGranted => 'Ruhusa za mahali ulipo zimetolewa! Inaelekea eneo lako...';

  @override
  String get locationFound => 'Mahali ulipo pamepatikana! Ramani imesahihishwa kwa nafasi yako ya sasa.';

  @override
  String get errorRequestingLocationPermission => 'Imeshindikana kupata mahali ulipo';

  @override
  String get driverEmail => '<EMAIL>';

  @override
  String get driverPhone => '+255000000000';

  @override
  String get notAvailable => 'Hazipatikani';

  @override
  String get driverProfile => 'Wasifu wa dereva';

  @override
  String get manageDriverProfile => 'Dhibiti wasifu wa dereva';

  @override
  String get viewWalletBalance => 'Angalia salio la mkoba na miamala';

  @override
  String get walletBalance => 'Salio la mkoba';

  @override
  String get plateNumber => 'Nambari ya bango';

  @override
  String get paymentPreference => 'Upendeleo wa malipo';

  @override
  String get rideHistory => 'Historia ya safari';

  @override
  String get active => 'Inatumika';

  @override
  String get completed => 'Imekamilika';

  @override
  String get canceled => 'Imeghairiwa';

  @override
  String get calculating => 'Inahesabu...';

  @override
  String get unknown => 'Haijulikani';

  @override
  String get driverNotAssigned => 'Dereva hajachaguliwa';

  @override
  String get locationUnavailable => 'Mahali hapapatikani';

  @override
  String get calculatingArrival => 'Inahesabu muda wa kufika...';

  @override
  String get arrivingNow => 'Anafika sasa';

  @override
  String get arrivesInMinute => 'Atafika dakika 1';

  @override
  String get arrivesInMinutes => 'Atafika dakika';

  @override
  String get noActiveRides => 'Hakuna safari inayoendelea';

  @override
  String get noCompletedRides => 'Hakuna safari zilizokwisha';

  @override
  String get noCanceledRides => 'Hakuna safari zilizobatilishwa';

  @override
  String get unknownDriver => 'Dereva asiyejulikana';

  @override
  String get cost => 'GHARAMA';

  @override
  String get date => 'TAREHE';

  @override
  String get estimatedTripTime => 'Muda wa safari unaotarajiwa ';

  @override
  String get contactDriver => 'Wasiliana na dereva';

  @override
  String get cancelRide => 'Ghairi safari';

  @override
  String get rateThisRide => 'Toa wastani wa safari Hii';

  @override
  String get bookAgain => 'Agiza tena';

  @override
  String get tripDuration => 'Muda wa safari: ';

  @override
  String get chooseYourRide => 'Chagua safari Yako';

  @override
  String get backToHome => 'Rudi nyumbani';

  @override
  String get twoWheeler => 'Usafiri wa magurudumu mawili';

  @override
  String get fourWheeler => 'Usafiri wa magurudumu manne';

  @override
  String get addLuggageSpace => 'Ongeza nafasi ya mizigo';

  @override
  String get haveAPromoCode => 'Una code ya punguzo?';

  @override
  String get enterPromoCode => 'Ingiza code ya punguzo';

  @override
  String get apply => 'Tumia';

  @override
  String get continueButton => 'Endelea';

  @override
  String get paymentMethod => 'Njia ya malipo';

  @override
  String get bodaboda => 'Bodaboda';

  @override
  String get bajaj => 'Bajaj';

  @override
  String get guta => 'Guta';

  @override
  String get carry => 'Carry';

  @override
  String get townace => 'Townace';

  @override
  String get capacity => 'Uwezo: ';

  @override
  String get lookingForDriver => 'Dereva anatafutwa';

  @override
  String get findingBestDriver => 'Tunatafuta dereva bora kwa ajili yako';

  @override
  String get tripInProgress => 'Safari inayoendelea';

  @override
  String get distance => 'Umbali';

  @override
  String get time => 'Muda';

  @override
  String get price => 'Bei';

  @override
  String get endTrip => 'Maliza safari';

  @override
  String get completeTrip => 'Maliza safari';

  @override
  String get areYouSureEndTrip => 'Je! Una hakika unataka kumaliza safari hii?';

  @override
  String get complete => 'Maliza';

  @override
  String get editProfile => 'Badilisha taarifa za wasifu';

  @override
  String get profilePicture => 'Picha ya Wasifu';

  @override
  String get firstName => 'Jina la kwanza';

  @override
  String get firstNameRequired => 'Jina la kwanza linahitajika';

  @override
  String get middleName => 'Jina la kati (Hiari)';

  @override
  String get lastName => 'Jina la mwisho';

  @override
  String get lastNameRequired => 'Jina la mwisho linahitajika';

  @override
  String get emailAddress => 'Anwani ya barua pepe';

  @override
  String get emailRequired => 'Barua pepe inahitajika';

  @override
  String get invalidEmailFormat => 'Barua pepe hii si sahihi';

  @override
  String get phoneNumberRequired => 'Nambari ya simu inahitajika';

  @override
  String get phoneNumberMinDigits => 'Nambari ya simu inapaswa kuwa na nambari 9 au zaidi';

  @override
  String get region => 'Mkoa';

  @override
  String get selectRegion => 'Chagua mkoa';

  @override
  String get saveChanges => 'Hifadhi mabadiliko';

  @override
  String get errorSavingProfile => 'Hitilafu imetokea wakati wa kukusanya wasifu:';

  @override
  String get reviews => 'Maoni';

  @override
  String get failedToLoadReviews => 'Imeshindikana kupata maoni';

  @override
  String get retry => 'Jaribu Tena';

  @override
  String get noReviewsAvailable => 'Hakuna maoni yoyote';

  @override
  String get basedOnCustomerFeedback => 'Kulingana na maoni ya wateja';

  @override
  String get anonymous => 'Hajulikani';

  @override
  String get daysAgo => 'idadi ya siku zilizopita';

  @override
  String get hoursAgo => 'idadi ya masaa yaliyopita';

  @override
  String get recently => 'Hivi karibuni';

  @override
  String get findingDriver => 'Dereva anatafutwa...';

  @override
  String get reject => 'Kataa';

  @override
  String get minutesToDelivery => 'Dakika za kupeleka';

  @override
  String get callRecipient => 'Piga simu kwa mpokeaji';

  @override
  String get startDropOffProcess => 'Anza mchakato wa kufikisha mzigo';

  @override
  String get deliveryCompleted => 'Safari imekamilika!';

  @override
  String get pleaseRateDriver => 'Tafadhali kadiri dereva wako...';

  @override
  String get cannotRateDriver => 'Imeshindikana kutoa wastani: Taarifa za dereva hazipatikani';

  @override
  String get deliveryInProgress => 'Safari inayoendelea';

  @override
  String get driverName => 'Jina la dereva';

  @override
  String get driverStats => 'Takwimu za dereva';

  @override
  String get selectLuggageSize => 'Chagua ukubwa wa Mizigo';

  @override
  String get personalItem => 'Mzigo binafsi';

  @override
  String get internationalCarryOn => 'Kubeba kimataifa';

  @override
  String get domesticCarryOn => 'Kubeba ndani ya nchi';

  @override
  String get smallChecked => 'Tumia mfuko mdogo';

  @override
  String get mediumChecked => 'Tumia mfuko wa kati';

  @override
  String get takeProofOfPickupParcel => 'Chukua ushahidi wa mzigo iliyochukuliwa';

  @override
  String get requestARide => 'Agiza usafiri';

  @override
  String get rideAmount => 'Gharama ya safari:';

  @override
  String get additionalCost => 'Gharama za ziada:';

  @override
  String get total => 'Jumla:';

  @override
  String get kgs => 'kgs';

  @override
  String get yourTravelTakes => 'Safari yako inachukua dakika 13.';

  @override
  String get findingNearestRide => 'Kutafuta safari karibu...';

  @override
  String get rideway => 'Njia';

  @override
  String get affordableRides => 'Safari za bei nafuu, yote kwa ajili yako';

  @override
  String get ridewaySuv => 'Njia ya SUV';

  @override
  String get luxuryRides => 'Safari za kishua';

  @override
  String get luggage => 'Mizigo';

  @override
  String get discountCode => 'Code ya kupunguza bei';

  @override
  String get enterDiscountCode => 'Weka code ya kupunguza bei';

  @override
  String get enterCode => 'Weka code';

  @override
  String get aboutSepesha => 'Kuhusu Sepesha';

  @override
  String get ourMission => 'Dhamira yetu';

  @override
  String get supportEmail => 'Barua pepe: <EMAIL>';

  @override
  String get supportPhone => 'Simu: +255 123 456 789';

  @override
  String get website => 'Tovuti: www.sepesha.com';

  @override
  String get address => 'Anwani:\nLorem ipsum street, 123\nDar es Salaam, Tanzania';

  @override
  String get legal => 'Kisheria';

  @override
  String get termsOfService => 'Masharti ya huduma';

  @override
  String get endUserLicenseAgreement => 'Makubaliano ya leseni ya mtumiaji wa mwisho';

  @override
  String get copyright => '© 2024 Sepesha. Haki zote zimehifadhiwa.';

  @override
  String get failedToLoadMessages => 'Imeshindikana kupakia ujumbe';

  @override
  String get noMessagesYet => 'Hakuna ujumbe bado';

  @override
  String get startConversation => 'Anza mazungumzo kwa kutuma ujumbe';

  @override
  String get typing => 'anaandika...';

  @override
  String get online => 'Yupo mtandaoni';

  @override
  String get lastSeenRecently => 'Alionekana hivi karibuni';

  @override
  String get viewProfile => 'Angalia wasifu';

  @override
  String get clearChat => 'Futa mazungumzo';

  @override
  String get blockUser => 'Zuia mtumiaji';

  @override
  String get areYouSureClearChat => 'Una uhakika unataka kufuta mazungumzo haya? Kitendo hiki hakiwezi kubadilishwa.';

  @override
  String get areYouSureBlockUser => 'Una uhakika unataka kumzuia mtumiaji huyu?';

  @override
  String get block => 'Zuia';

  @override
  String get copy => 'Nakili';

  @override
  String get reply => 'Jibu';

  @override
  String get delete => 'Futa';

  @override
  String get messageCopied => 'Ujumbe umenakiliwa kwenye clipboard';

  @override
  String get messageDeleted => 'Ujumbe umefutwa';

  @override
  String get replyFunctionalityComingSoon => 'Kipengele cha kujibu kinakuja hivi karibuni';

  @override
  String get voiceCallFeatureComingSoon => 'Kipengele cha simu ya sauti kinakuja hivi karibuni';

  @override
  String get videoCallFeatureComingSoon => 'Kipengele cha simu ya video kinakuja hivi karibuni';

  @override
  String get profileViewComingSoon => 'Mhakiki wa wasifu unakuja hivi karibuni';

  @override
  String get chatCleared => 'Mazungumzo yamefutwa';

  @override
  String get userBlocked => 'Mtumiaji amezuiwa';

  @override
  String get saveImageComingSoon => 'Uhifadhi wa picha unakuja hivi karibuni';

  @override
  String get failedToLoadImage => 'Imeshindikana kupakia picha';

  @override
  String get failedToShareLocation => 'Imeshindikana kushiriki mahali: ';

  @override
  String get userType => 'Aina ya mtumiaji';

  @override
  String get accountStatus => 'Hali ya akaunti';

  @override
  String get verified => 'Imehakikiwa';

  @override
  String get unverified => 'Haijahakikiwa';

  @override
  String get walletBalanceTzs => 'Salio la mkoba (TZS)';

  @override
  String get walletBalanceUsd => 'Salio la mkoba (USD)';

  @override
  String get accountInformation => 'Maelezo ya akaunti';

  @override
  String get success => 'Mafanikio';

  @override
  String get profileUpdatedSuccessfully => 'Wasifu umesasishwa kwa mafanikio';

  @override
  String get error => 'Hitilafu';

  @override
  String get failedToUpdateProfile => 'Imeshindikana kusasisha wasifu';

  @override
  String get noProfileDataAvailable => 'Hakuna data ya wasifu inayopatikana';

  @override
  String get enterLocation => 'Ingiza mahali ulipo';

  @override
  String get currentLocation => 'Mahali ulipo sasa';

  @override
  String get loadingPaymentMethods => 'Inapakia njia za malipo...';

  @override
  String get paymentMethodUpdated => 'Njia ya malipo imesasishwa';

  @override
  String get failedToUpdatePaymentMethod => 'Imeshindikana kusasisha njia ya malipo';

  @override
  String get walletBalanceDetails => 'Maelezo ya salio la mkoba';

  @override
  String get tzsBalance => 'Salio la TZS';

  @override
  String get usdBalance => 'Salio la USD';

  @override
  String get walletReadyForPayments => 'Mkoba wako uko tayari kwa malipo';

  @override
  String get addFundsToWallet => 'Ongeza fedha kwenye mkoba wako kutumia njia hii ya malipo';

  @override
  String get unableToLoadWalletBalance => 'Haiwezekani kupakia salio la mkoba';

  @override
  String get close => 'Funga';

  @override
  String get refresh => 'Onyesha upya';

  @override
  String get done => 'Imekamilika';

  @override
  String get selected => 'Imechaguliwa';

  @override
  String get searchRide => 'Tafuta safari';

  @override
  String get whereAreYouGoing => 'Unaenda wapi?';

  @override
  String get findRide => 'Pata safari';

  @override
  String get accountStatistics => 'Takwimu za akaunti';

  @override
  String get averageRating => 'Kadiri ya wastani';

  @override
  String get walletTzs => 'Mkoba (TZS)';

  @override
  String get walletUsd => 'Mkoba (USD)';

  @override
  String get preferredPaymentMethod => 'Njia ya malipo iliyochaguliwa';

  @override
  String get verifiedAccount => 'Akaunti imehakikiwa';

  @override
  String get pendingVerification => 'Inasubiri uhakiki';
}
