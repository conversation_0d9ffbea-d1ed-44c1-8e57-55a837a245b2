import 'package:flutter/material.dart';
import 'package:sepesha_app/Utilities/app_color.dart';
import 'package:sepesha_app/components/app_button.dart';
import 'package:sepesha_app/widgets/rating_dialog.dart';
import 'package:sepesha_app/components/home/<USER>';
import 'package:sepesha_app/provider/ride_provider.dart';

class TripInProgressContent extends StatelessWidget {
  final RideProvider provider;

  const TripInProgressContent({super.key, required this.provider});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Sheet<PERSON>andle(),
          const SizedBox(height: 16),
          const Text(
            'Trip in progress',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(Icons.location_on, color: AppColor.primary),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        provider.destinationAddress,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const Text('Destination'),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildTripMetric('Distance', '2.4 km'),
              _buildTripMetric('Time', '8 min'),
              _buildTripMetric('Price', '£10.50'),
            ],
          ),
          const SizedBox(height: 16),
          ContinueButton(
            isLoading: false,
            text: "End Trip",
            onPressed: () => _showTripCompletionDialog(context, provider),
            backgroundColor: AppColor.primary,
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildTripMetric(String label, String value) {
    return Column(
      children: [
        Text(label, style: TextStyle(color: Colors.grey[600])),
        Text(value, style: const TextStyle(fontWeight: FontWeight.bold)),
      ],
    );
  }

  void _showTripCompletionDialog(BuildContext context, RideProvider provider) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Complete Trip'),
            content: const Text('Are you sure you want to end this trip?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  _completeTrip(context, provider);
                },
                child: const Text('Complete'),
              ),
            ],
          ),
    );
  }

  void _completeTrip(BuildContext context, RideProvider provider) {
    // Complete the trip
    provider.resetToInitialState();

    // Show rating dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => RatingDialog(
            driverId: 'driver_id_here', // Get from provider
            driverName: provider.driverName,
            onRatingSubmitted: () {
              // Rating completed, stay on current screen
            },
          ),
    );
  }
}
