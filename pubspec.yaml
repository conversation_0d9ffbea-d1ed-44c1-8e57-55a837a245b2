name: sepesha_app
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.3+6

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.

  cupertino_icons: ^1.0.8
  sign_in_button: ^3.2.0
  intl_phone_field: ^3.2.0
  otp_text_field: ^1.1.3
  flutter_map: ^8.1.1
  firebase_core: any
  firebase_messaging: any
  flutter_local_notifications: any
  # latlong2: ^0.9.1

  # ipf_flutter_starter_pack:
  #   path: /Users/<USER>/Downloads/iPF_Flutter_Starter_Pack-develop
  # geolocator: ^14.0.0
  http: ^1.4.0
  
  flutter_dotenv: ^5.2.1
  flutter_svg: ^2.0.17
  file_picker: ^10.0.0
  google_fonts: ^6.2.1
  hugeicons: ^0.0.10
  intl: any
  flutter_localizations:
    sdk: flutter
  cached_network_image: any
  permission_handler: any
  flutter_rating_bar: ^4.0.1
  provider: ^6.1.5
  google_maps_flutter: ^2.12.2
  url_launcher: ^6.3.1
  location: ^8.0.0
  # agora_rtc_engine: ^6.5.2
  # permission_handler: ^12.0.0+1
  change_app_package_name: ^1.5.0
  image_picker: ^1.1.2
  image_cropper: any
  geolocator: ^13.0.4
  flutter_polyline_points: ^2.1.0
  geocoding: ^4.0.0
  jwt_decoder: ^2.0.1
  action_slider: ^0.7.0
  archive: ^4.0.7
  web_socket_channel: ^3.0.3
  shared_preferences: ^2.5.3 
  flutter_gen: ^5.11.0
  


dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # Enable internationalization
  generate: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - .env
    - assets/images/
    - assets/icons/

    # - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Poppins
      fonts:
        - asset: assets/fonts/Poppins-Thin.ttf
          weight: 100
        - asset: assets/fonts/Poppins-ThinItalic.ttf
          weight: 100
          style: italic
        - asset: assets/fonts/Poppins-ExtraLight.ttf
          weight: 200
        - asset: assets/fonts/Poppins-ExtraLightItalic.ttf
          weight: 200
          style: italic
        - asset: assets/fonts/Poppins-Light.ttf
          weight: 300
        - asset: assets/fonts/Poppins-LightItalic.ttf
          weight: 300
          style: italic
        - asset: assets/fonts/Poppins-Regular.ttf
          weight: 400
        - asset: assets/fonts/Poppins-Italic.ttf
          weight: 400
          style: italic
        - asset: assets/fonts/Poppins-Medium.ttf
          weight: 500
        - asset: assets/fonts/Poppins-MediumItalic.ttf
          weight: 500
          style: italic
        - asset: assets/fonts/Poppins-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Poppins-SemiBoldItalic.ttf
          weight: 600
          style: italic
        - asset: assets/fonts/Poppins-Bold.ttf
          weight: 700
        - asset: assets/fonts/Poppins-BoldItalic.ttf
          weight: 700
          style: italic
        - asset: assets/fonts/Poppins-ExtraBold.ttf
          weight: 800
        - asset: assets/fonts/Poppins-ExtraBoldItalic.ttf
          weight: 800
          style: italic
        - asset: assets/fonts/Poppins-Black.ttf
          weight: 900
        - asset: assets/fonts/Poppins-BlackItalic.ttf
          weight: 900
          style: italic
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
